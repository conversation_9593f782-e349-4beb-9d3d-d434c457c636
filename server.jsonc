{"log": {"loglevel": "debug"}, "stats": {}, "policy": {"levels": {"0": {"statsUserUplink": true, "statsUserDownlink": true}}}, "inbounds": [{"port": 10000, "listen": "127.0.0.1", "protocol": "vmess", "settings": {"clients": [{"email": "<EMAIL>", "id": "b831381d-6324-4d53-ad4f-8cda48b30811", "level": 0}]}, "streamSettings": {"network": "ws", "wsSettings": {"path": "/ray"}}, "tag": "main"}], "outbounds": [{"protocol": "freedom"}, {"protocol": "blackhole", "tag": "blocked"}], "routing": {"rules": [{"type": "field", "ip": ["0.0.0.0/8", "10.0.0.0/8", "**********/10", "*********/8", "***********/16", "**********/12", "*********/24", "*********/24", "***********/16", "**********/15", "************/24", "***********/24", "::1/128", "fc00::/7", "fe80::/10"], "outboundTag": "blocked"}]}}