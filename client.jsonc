{"log": {"loglevel": "debug"}, "inbounds": [{"port": 1080, "listen": "127.0.0.1", "protocol": "socks", "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true}}], "outbounds": [{"protocol": "vmess", "streamSettings": {"network": "ws", "wsSettings": {"path": "/ray"}}, "mux": {"enabled": true}, "settings": {"vnext": [{"address": "127.0.0.1", "port": 10000, "users": [{"id": "b831381d-6324-4d53-ad4f-8cda48b30811", "alterId": 0}]}]}}]}